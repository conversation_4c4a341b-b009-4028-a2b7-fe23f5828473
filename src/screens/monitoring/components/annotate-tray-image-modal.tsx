import Image from 'next/image';
import { ReactNode, SyntheticEvent, useState } from 'react';
import {
  Box,
  DialogFooter,
  EmptyState,
  Flex,
  Heading,
  Text,
  useDisclosure,
  VStack,
  chakra,
  Separator,
  Icon
} from '@chakra-ui/react';
import { Checkbox } from '@components/ui/checkbox';
import { BaseButton } from '@components/base/base-button';
import {
  DialogActionTrigger,
  DialogBody,
  DialogContent,
  DialogHeader,
  DialogRoot,
  DialogTitle,
  DialogTrigger
} from '@components/ui/dialog';
import { Tray } from '@xpertsea/module-farm-sdk';
import { TransformComponent, TransformWrapper, useControls } from 'react-zoom-pan-pinch';
import { LuListX, LuZoomIn, LuZoomOut, LuMaximize2 } from 'react-icons/lu';
import { CloseButton } from '@components/ui/close-button';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { BaseFormTextarea } from '@components/form/base-form-textarea';
import { Controller, useForm } from 'react-hook-form';

type Gill = 'darkGill';
type AbdomenColor = 'paleAbdomen' | 'redAbdomen' | 'blackAbdomen';
type TailColor = 'redTail' | 'turquoiseTail' | 'brownTail' | 'blackTail';
type AppendageFlag = 'redAppendage' | 'yellowAppendage' | 'brownAppendage' | 'brokenAppendage' | 'missingAppendage';

type Option<Value> = {
  value: Value;
  label: string;
};
const tailOptions: Option<TailColor>[] = [
  { value: 'redTail', label: 'Red' },
  { value: 'turquoiseTail', label: 'Turquoise' },
  { value: 'brownTail', label: 'Brown' },
  { value: 'blackTail', label: 'Black' }
];
const gillOptions: Option<Gill>[] = [{ value: 'darkGill', label: 'Dark' }];
const abdomenOptions: Option<AbdomenColor>[] = [
  { value: 'paleAbdomen', label: 'Pale' },
  { value: 'redAbdomen', label: 'Red' },
  { value: 'blackAbdomen', label: 'Black' }
];
const appendageOptions: Option<AppendageFlag>[] = [
  { value: 'redAppendage', label: 'Red' },
  { value: 'yellowAppendage', label: 'Yellow' },
  { value: 'brownAppendage', label: 'Brown' },
  { value: 'brokenAppendage', label: 'Broken' },
  { value: 'missingAppendage', label: 'Missing' }
];

type TrayAnnotationForm = {
  shrimps: {
    id: string;

    isGillEnabled?: boolean;
    isTailEnabled?: boolean;
    isHealthyEnabled?: boolean;
    isAbdomenEnabled?: boolean;
    isAppendageEnabled?: boolean;

    gill: { [key in Gill]?: boolean };
    tail: { [key in TailColor]?: boolean };
    abdomen: { [key in AbdomenColor]?: boolean };
    appendage: { [key in AppendageFlag]?: boolean };

    comment?: string;
  }[];
};

type Shrimp = {
  id: string;
  gut: [number, number];
  alternative_polygons: {
    refined: [[number, number], [number, number], [number, number], [number, number]];
    reconciled: [[number, number], [number, number], [number, number], [number, number]];
    expanded: [[number, number], [number, number], [number, number], [number, number]];
  };
};

type AnnotateTrayImageModalProps = {
  tray: Tray;
  children: ReactNode;
};

export function AnnotateTrayImageModal(props: AnnotateTrayImageModalProps) {
  const { tray, children } = props;

  const { images } = tray ?? {};
  const [firstTrayImage] = images ?? [];
  const { computerVisionData, imageUrl } = firstTrayImage ?? {};
  const { shrimps } = (computerVisionData ?? {}) as {
    shrimps?: Shrimp[];
  };

  const { onOpen, onClose, open } = useDisclosure();
  const popoverDisclosure = useDisclosure();
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);
  const [isGutPointsVisible, setIsGutPointsVisible] = useState(true);
  const [pastedImage, setPastedImage] = useState<string | null>(null);
  const [isHoverPolygonVisible, setIsHoverPolygonVisible] = useState(true);
  const [imageDimensions, setImageDimensions] = useState<{ width: number; height: number } | null>(null);
  const [hoveredShrimp, setHoveredShrimp] = useState<{ id: string; gut: [number, number]; index: number } | null>(null);
  const [selectedShrimp, setSelectedShrimp] = useState<{ id: string; gut: [number, number]; index: number } | null>(
    null
  );

  const { control, watch, resetField } = useForm<TrayAnnotationForm>({
    defaultValues: { shrimps: [] }
  });

  const handlePasteClick = async () => {
    try {
      const permission = await navigator.permissions.query({ name: 'clipboard-read' as PermissionName });
      if (permission.state === 'denied') {
        alert('Clipboard permission denied. Please allow clipboard access and try again.');
        return;
      }

      const items = await navigator.clipboard.read();
      let imageFound = false;

      for (const item of items) {
        for (const type of item.types) {
          if (type.startsWith('image/')) {
            const blob = await item.getType(type);
            const reader = new FileReader();
            reader.onload = (event) => {
              setPastedImage(event.target?.result as string);
              setImageError(false);
              setImageLoading(true);
            };
            reader.onerror = () => {
              setImageError(true);
            };
            reader.readAsDataURL(blob);
            imageFound = true;
            break;
          }
        }
        if (imageFound) break;
      }
    } catch (error) {
      setImageError(true);
    }
  };

  const handleImageLoad = (event: SyntheticEvent<HTMLImageElement>) => {
    const img = event.currentTarget;
    setImageDimensions({ width: img.naturalWidth, height: img.naturalHeight });
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
  };

  const isTailEnabled = watch(`shrimps.${selectedShrimp?.index}.isTailEnabled`);
  const isGillEnabled = watch(`shrimps.${selectedShrimp?.index}.isGillEnabled`);
  const isHealthyEnabled = watch(`shrimps.${selectedShrimp?.index}.isHealthyEnabled`);
  const isAbdomenEnabled = watch(`shrimps.${selectedShrimp?.index}.isAbdomenEnabled`);
  const isAppendageEnabled = watch(`shrimps.${selectedShrimp?.index}.isAppendageEnabled`);

  return (
    <>
      <DialogRoot size='full' open={open} restoreFocus={false} onOpenChange={(e) => (e.open ? onOpen() : onClose())}>
        <DialogTrigger as={Box} asChild cursor='pointer' userSelect='none'>
          {children}
        </DialogTrigger>
        <DialogContent rounded='none' display='flex' flexDirection='column' height='100vh' bgColor='gray.100'>
          <DialogHeader>
            <DialogTitle as={Box}>
              <Heading size='heavy300'>Annotate tray</Heading>
              <Text size='light300' mt='xs' color='text.gray.weak'>
                Click on points to inspect and tag the shrimp in the image.
              </Text>
            </DialogTitle>
          </DialogHeader>
          <DialogBody px='lg' pb='2lg' pt={0}>
            <Flex height='100%'>
              <Flex flex={1} position='relative' overflow='hidden' align='center' justify='center'>
                {!imageError && (pastedImage || imageUrl) && (
                  <TransformWrapper>
                    <ZoomControls isVisible={!imageLoading} />
                    <TransformComponent wrapperStyle={{ borderRadius: '16px', cursor: 'grab' }}>
                      <Image
                        width={0}
                        height={0}
                        sizes='100vw'
                        unoptimized={true}
                        alt='Annotate tray image'
                        src={pastedImage || imageUrl}
                        onLoad={handleImageLoad}
                        onError={handleImageError}
                        style={{
                          width: '100%',
                          height: 'auto',
                          maxHeight: '80vh',
                          objectFit: 'contain'
                        }}
                      />
                      {!imageLoading && imageDimensions && shrimps && isGutPointsVisible && (
                        <Icon
                          as='svg'
                          top={0}
                          left={0}
                          w='100%'
                          height='100%'
                          pos='absolute'
                          viewBox={`0 0 ${imageDimensions.width} ${imageDimensions.height}`}
                        >
                          {shrimps.map((shrimp, index) => {
                            const [x, y] = shrimp.gut;
                            return (
                              <chakra.circle
                                key={shrimp.id || index}
                                data-shrimp-id={shrimp.id}
                                r={'12px'}
                                strokeWidth={2}
                                stroke='red.600'
                                cx={x.toString()}
                                cy={y.toString()}
                                fill='red.600/40'
                                cursor='pointer'
                                onClick={() => {
                                  setSelectedShrimp({ id: shrimp.id, gut: shrimp.gut, index });
                                  popoverDisclosure.onOpen();
                                }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.opacity = '1';
                                  if (!isHoverPolygonVisible) {
                                    e.currentTarget.style.strokeWidth = '3';
                                  }
                                  setHoveredShrimp({ id: shrimp.id, gut: shrimp.gut, index });
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.opacity = '0.8';
                                  e.currentTarget.style.strokeWidth = '2';
                                  setHoveredShrimp(null);
                                }}
                              />
                            );
                          })}
                          <DrawPolygon
                            isVisible={hoveredShrimp && isHoverPolygonVisible}
                            shrimp={shrimps.find((s) => s.id === hoveredShrimp?.id)}
                          />
                        </Icon>
                      )}
                    </TransformComponent>
                  </TransformWrapper>
                )}

                {/* Annotation modal rendered outside TransformComponent */}
                {selectedShrimp && popoverDisclosure.open && (
                  <>
                    {/* Backdrop */}
                    <Box
                      position='fixed'
                      top={0}
                      left={0}
                      right={0}
                      bottom={0}
                      bg='blackAlpha.600'
                      zIndex={999}
                      onClick={() => {
                        popoverDisclosure.onClose();
                        setSelectedShrimp(null);
                      }}
                    />
                    {/* Modal content */}
                    <Box
                      position='fixed'
                      top='50%'
                      left='50%'
                      transform='translate(-50%, -50%)'
                      zIndex={1000}
                      w='490px'
                      rounded='2xl'
                      bg='white'
                      shadow='xl'
                      border='1px solid'
                      borderColor='gray.200'
                      p='md'
                      maxH='80vh'
                      overflowY='auto'
                    >
                      <Flex gap='md' flexDir='column'>
                        <Flex align='center' justify='space-between'>
                          <Text size='label100'>Add annotation for shrimp {selectedShrimp.index + 1}</Text>
                          <Flex align='center' gap='sm-alt'>
                            <BaseButton disabled size='sm'>
                              Save changes
                            </BaseButton>
                            <CloseButton size='sm' onClick={popoverDisclosure.onClose} />
                          </Flex>
                        </Flex>
                        <Flex flexDir='column' gap='2lg'>
                          <Flex gap='md' flexDir='column' css={{ '& svg path': { fill: 'white' } }}>
                            <Text size='label200'>Tags</Text>
                            <Controller
                              control={control}
                              name={`shrimps.${selectedShrimp.index}.isHealthyEnabled`}
                              render={({ field: { value, onChange, ...rest } }) => (
                                <BaseFormCheckbox
                                  {...rest}
                                  checked={value}
                                  id={`shrimps.${selectedShrimp.index}.isHealthyEnabled`}
                                  onCheckedChange={({ checked }) => {
                                    onChange(checked);
                                    if (checked) {
                                      resetField(`shrimps.${selectedShrimp.index}.isTailEnabled`);
                                      resetField(`shrimps.${selectedShrimp.index}.isAbdomenEnabled`);
                                      resetField(`shrimps.${selectedShrimp.index}.isAppendageEnabled`);

                                      resetField(`shrimps.${selectedShrimp.index}.tail`);
                                      resetField(`shrimps.${selectedShrimp.index}.abdomen`);
                                      resetField(`shrimps.${selectedShrimp.index}.appendage`);
                                    }
                                  }}
                                  label={
                                    <Text size='label200' whiteSpace='nowrap'>
                                      Healthy
                                    </Text>
                                  }
                                />
                              )}
                            />
                            <Separator borderTopWidth='0.5px' borderColor='gray.400' />
                            <Flex flexDir='column' gap='md'>
                              <Controller
                                control={control}
                                name={`shrimps.${selectedShrimp.index}.isTailEnabled`}
                                render={({ field: { value, onChange, ...rest } }) => (
                                  <BaseFormCheckbox
                                    {...rest}
                                    checked={value}
                                    disabled={isHealthyEnabled}
                                    id={`shrimps.${selectedShrimp.index}.isTailEnabled`}
                                    onCheckedChange={({ checked }) => {
                                      onChange(checked);
                                      if (!checked) {
                                        resetField(`shrimps.${selectedShrimp.index}.tail`);
                                      }
                                    }}
                                    label={
                                      <Text size='label200' whiteSpace='nowrap'>
                                        Tail
                                      </Text>
                                    }
                                  />
                                )}
                              />
                              {isTailEnabled && (
                                <Flex align='center' gap='md'>
                                  {tailOptions.map((option) => (
                                    <Controller
                                      control={control}
                                      key={option.value}
                                      name={`shrimps.${selectedShrimp.index}.tail.${option.value}`}
                                      render={({ field: { value, onChange, ...rest } }) => (
                                        <BaseFormCheckbox
                                          {...rest}
                                          checked={value}
                                          id={`shrimps.${selectedShrimp.index}.tail.${option.value}`}
                                          onCheckedChange={({ checked }) => onChange(checked)}
                                          label={
                                            <Text size='label200' whiteSpace='nowrap'>
                                              {option.label}
                                            </Text>
                                          }
                                        />
                                      )}
                                    />
                                  ))}
                                </Flex>
                              )}
                            </Flex>
                            <Separator borderTopWidth='0.5px' borderColor='gray.400' />
                            <Flex flexDir='column' gap='md'>
                              <Controller
                                control={control}
                                name={`shrimps.${selectedShrimp.index}.isGillEnabled`}
                                render={({ field: { value, onChange, ...rest } }) => (
                                  <BaseFormCheckbox
                                    {...rest}
                                    checked={value}
                                    disabled={isHealthyEnabled}
                                    id={`shrimps.${selectedShrimp.index}.isGillEnabled`}
                                    onCheckedChange={({ checked }) => {
                                      onChange(checked);
                                      if (!checked) {
                                        resetField(`shrimps.${selectedShrimp.index}.gill`);
                                      }
                                    }}
                                    label={
                                      <Text size='label200' whiteSpace='nowrap'>
                                        Gill
                                      </Text>
                                    }
                                  />
                                )}
                              />
                              {isGillEnabled && (
                                <Flex align='center' gap='md'>
                                  {gillOptions.map((option) => (
                                    <Controller
                                      control={control}
                                      key={option.value}
                                      name={`shrimps.${selectedShrimp.index}.gill.${option.value}`}
                                      render={({ field: { value, onChange, ...rest } }) => (
                                        <BaseFormCheckbox
                                          {...rest}
                                          checked={value}
                                          id={`shrimps.${selectedShrimp.index}.gill.${option.value}`}
                                          onCheckedChange={({ checked }) => onChange(checked)}
                                          label={
                                            <Text size='label200' whiteSpace='nowrap'>
                                              {option.label}
                                            </Text>
                                          }
                                        />
                                      )}
                                    />
                                  ))}
                                </Flex>
                              )}
                            </Flex>
                            <Separator borderTopWidth='0.5px' borderColor='gray.400' />
                            <Flex flexDir='column' gap='md'>
                              <Controller
                                control={control}
                                name={`shrimps.${selectedShrimp.index}.isAbdomenEnabled`}
                                render={({ field: { value, onChange, ...rest } }) => (
                                  <BaseFormCheckbox
                                    {...rest}
                                    checked={value}
                                    disabled={isHealthyEnabled}
                                    id={`shrimps.${selectedShrimp.index}.isAbdomenEnabled`}
                                    onCheckedChange={({ checked }) => {
                                      onChange(checked);
                                      if (!checked) {
                                        resetField(`shrimps.${selectedShrimp.index}.abdomen`);
                                      }
                                    }}
                                    label={
                                      <Text size='label200' whiteSpace='nowrap'>
                                        Abdomen
                                      </Text>
                                    }
                                  />
                                )}
                              />
                              {isAbdomenEnabled && (
                                <Flex align='center' gap='md'>
                                  {abdomenOptions.map((option) => (
                                    <Controller
                                      control={control}
                                      key={option.value}
                                      name={`shrimps.${selectedShrimp.index}.abdomen.${option.value}`}
                                      render={({ field: { value, onChange, ...rest } }) => (
                                        <BaseFormCheckbox
                                          {...rest}
                                          checked={value}
                                          id={`shrimps.${selectedShrimp.index}.abdomen.${option.value}`}
                                          onCheckedChange={({ checked }) => onChange(checked)}
                                          label={
                                            <Text size='label200' whiteSpace='nowrap'>
                                              {option.label}
                                            </Text>
                                          }
                                        />
                                      )}
                                    />
                                  ))}
                                </Flex>
                              )}
                            </Flex>
                            <Separator borderTopWidth='0.5px' borderColor='gray.400' />
                            <Flex flexDir='column' gap='md'>
                              <Controller
                                control={control}
                                name={`shrimps.${selectedShrimp.index}.isAppendageEnabled`}
                                render={({ field: { value, onChange, ...rest } }) => (
                                  <BaseFormCheckbox
                                    {...rest}
                                    checked={value}
                                    disabled={isHealthyEnabled}
                                    id={`shrimps.${selectedShrimp.index}.isAppendageEnabled`}
                                    onCheckedChange={({ checked }) => {
                                      onChange(checked);
                                      if (!checked) {
                                        resetField(`shrimps.${selectedShrimp.index}.appendage`);
                                      }
                                    }}
                                    label={
                                      <Text size='label200' whiteSpace='nowrap'>
                                        Appendage
                                      </Text>
                                    }
                                  />
                                )}
                              />
                              {isAppendageEnabled && (
                                <Flex align='center' gap='md'>
                                  {appendageOptions.map((option) => (
                                    <Controller
                                      control={control}
                                      key={option.value}
                                      name={`shrimps.${selectedShrimp.index}.appendage.${option.value}`}
                                      render={({ field: { value, onChange, ...rest } }) => (
                                        <BaseFormCheckbox
                                          {...rest}
                                          checked={value}
                                          id={`shrimps.${selectedShrimp.index}.appendage.${option.value}`}
                                          onCheckedChange={({ checked }) => onChange(checked)}
                                          label={
                                            <Text size='label200' whiteSpace='nowrap'>
                                              {option.label}
                                            </Text>
                                          }
                                        />
                                      )}
                                    />
                                  ))}
                                </Flex>
                              )}
                            </Flex>
                          </Flex>
                          <Controller
                            control={control}
                            name={`shrimps.${selectedShrimp.index}.comment`}
                            render={({ field: { value, onChange, ...rest } }) => (
                              <BaseFormTextarea
                                {...rest}
                                rows={4}
                                resize='none'
                                value={value}
                                label='Comment'
                                id={`shrimps.${selectedShrimp.index}.comment`}
                                onChange={(e) => onChange(e.target.value)}
                              />
                            )}
                          />
                        </Flex>
                      </Flex>
                    </Box>
                  </>
                )}
                {(imageError || (!pastedImage && !imageUrl)) && (
                  <Flex flexDir='column' align='center' justify='center' h='100%' gap='sm-alt'>
                    {imageError && (
                      <>
                        <Text color='red.500'>Image failed to load.</Text>
                        <a href={imageUrl} target='_blank' rel='noopener noreferrer'>
                          <BaseButton size='sm'>Open image in new tab</BaseButton>
                        </a>
                      </>
                    )}
                    <Flex
                      p='md'
                      gap='sm-alt'
                      flexDir='column'
                      cursor='pointer'
                      borderRadius='xl'
                      userSelect='none'
                      border='0.5px dashed'
                      borderColor='gray.500'
                      onClick={handlePasteClick}
                    >
                      <Text fontWeight='bold' color='gray.700'>
                        Click here to paste an image from clipboard
                      </Text>
                      <Text size='light300' color='gray.500'>
                        Note: Only images from clipboard are accepted.
                      </Text>
                    </Flex>
                  </Flex>
                )}
              </Flex>
              <Flex flexDir='column' justify='space-between' width='360px' overflowY='auto' gap='3md'>
                <Flex align='center' gap='sm-alt'>
                  <BaseButton variant='secondary'>Share</BaseButton>
                  <BaseButton flex={1}>Mark remaining as healthy</BaseButton>
                </Flex>
                <Flex flexDir='column' gap='3md' flex={1}>
                  <Box rounded='2xl' p='md' bgColor='white'>
                    <Text size='label100' mb='sm-alt'>
                      Summary
                    </Text>
                    <Flex flexDir='column'>
                      <Flex
                        py='sm-alt'
                        align='center'
                        justify='space-between'
                        borderBottom='0.5px solid'
                        borderBottomColor='gray.200'
                      >
                        <Text size='label200'>Total shrimp</Text>
                        <Text size='label200'>{shrimps?.length || 0}</Text>
                      </Flex>
                      <Flex
                        py='sm-alt'
                        align='center'
                        justify='space-between'
                        borderBottom='0.5px solid'
                        borderBottomColor='gray.200'
                      >
                        <Text size='label200'>Total healthy shrimp</Text>
                        <Text size='label200'>0</Text>
                      </Flex>
                      <Flex pt='sm-alt' align='center' justify='space-between'>
                        <Text size='label200'>Total clinical signs</Text>
                        <Text size='label200'>0</Text>
                      </Flex>
                    </Flex>
                  </Box>
                  <Flex flexDir='column' rounded='2xl' p='md' bgColor='white' flex={1}>
                    <Text size='label100' mb='sm-alt'>
                      Annotations
                    </Text>
                    <EmptyState.Root flex={1} alignContent='center'>
                      <EmptyState.Content gap='sm-alt'>
                        <EmptyState.Indicator>
                          <LuListX />
                        </EmptyState.Indicator>
                        <VStack textAlign='center' gap={0}>
                          <EmptyState.Title>No annotations found</EmptyState.Title>
                          <EmptyState.Description>
                            Click on points to inspect and tag the shrimp in the image.
                          </EmptyState.Description>
                        </VStack>
                      </EmptyState.Content>
                    </EmptyState.Root>
                  </Flex>
                </Flex>
              </Flex>
            </Flex>
          </DialogBody>
          <DialogFooter bgColor='white' justifyContent='space-between' py='md' px='lg' shadow='elevation.200'>
            <Flex align='center' gap='sm-alt'>
              <Checkbox
                checked={isGutPointsVisible}
                onCheckedChange={(details) => setIsGutPointsVisible(!!details.checked)}
              >
                Toggle gut points
              </Checkbox>
              <Checkbox
                checked={isHoverPolygonVisible}
                onCheckedChange={(details) => setIsHoverPolygonVisible(!!details.checked)}
              >
                Toggle hover polygon
              </Checkbox>
            </Flex>
            <Flex align='center' gap='sm-alt'>
              <DialogActionTrigger asChild>
                <BaseButton variant='secondary'>Cancel</BaseButton>
              </DialogActionTrigger>
              <BaseButton>Submit</BaseButton>
            </Flex>
          </DialogFooter>
        </DialogContent>
      </DialogRoot>
    </>
  );
}

function ZoomControls({ isVisible }: { isVisible: boolean }) {
  const { zoomIn, zoomOut, resetTransform } = useControls();

  if (!isVisible) return;

  return (
    <Flex
      p='xs'
      left='50%'
      zIndex={1}
      bottom='1rem'
      gap='sm-alt'
      rounded='lg'
      align='center'
      bgColor='white'
      position='absolute'
      shadow='elevation.100'
      transform='translateX(-50%)'
    >
      <BaseButton size='sm' variant='outline' rounded='md' onClick={() => zoomIn()}>
        <LuZoomIn /> Zoom in
      </BaseButton>
      <BaseButton size='sm' variant='outline' rounded='md' onClick={() => zoomOut()}>
        <LuZoomOut /> Zoom out
      </BaseButton>
      <BaseButton size='sm' variant='outline' rounded='md' onClick={() => resetTransform()}>
        <LuMaximize2 /> Reset
      </BaseButton>
    </Flex>
  );
}

function DrawPolygon({ shrimp, isVisible }: { shrimp: Shrimp; isVisible: boolean }) {
  if (!isVisible) return;

  if (!shrimp?.alternative_polygons?.refined) return null;

  const points = shrimp.alternative_polygons.refined;
  const polygonPoints = points.map((point) => `${point[0]},${point[1]}`).join(' ');

  return (
    <chakra.polygon
      opacity='0.8'
      fill='red.500'
      strokeWidth='2'
      fillOpacity='0.2'
      stroke='red.500'
      pointerEvents='none'
      points={polygonPoints}
    />
  );
}
